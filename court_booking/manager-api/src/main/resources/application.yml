
server:
  port: 8887

  servlet:
    context-path: /

  tomcat:
    uri-encoding: UTF-8
    threads:
      min-spare: 50
      max: 1000

spring:
  application:
    name: manager-api
  cache:
    type: redis
  # Redis
  redis:
    #    host: ***************
    host: 127.0.0.1
    port: 6379
    #    password: 123456
    lettuce:
      pool:
        # 连接池最大连接数（使用负值表示没有限制） 默认 8
        max-active: 200
        # 连接池最大阻塞等待时间（使用负值表示没有限制） 默认 -1
        max-wait: 20
        # 连接池中的最大空闲连接 默认 8
        max-idle: 10
        # 连接池中的最小空闲连接 默认 8
        min-idle: 8
  # 文件大小上传配置
  datasource:
    driverClassName: com.mysql.cj.jdbc.Driver
    username: wiki2
    password: LStsbtzipfiGy6ZW
    url: ****************************************************************************************************************************************

minio:
  endpoint: http://127.0.0.1:9005
  accessKey: root
  secretKey: password
  secure: false
  bucketName: "weblog"


  servlet:
    multipart:
      max-file-size: 20MB
      max-request-size: 20MB
  tomcat:
    max-http-form-post-size: 20MB
    max-swallow-size: 20MB
  jackson:
    time-zone: GMT+8
    serialization:
      #关闭jackson 对json做解析
      fail-on-empty-beans: false

# 忽略鉴权url
ignored:
  urls:
    - /**
    - /wteam/**
    - /ws/**
    - /staff/alarmrecord/**
    - /editor-app/**
    - /actuator**
    - /actuator/**
    - /MP_verify_qSyvBPhDsPdxvOhC.txt
    - /weixin/**
    - /source/**
    - /staff/admin/login
    - /staff/admin/logo
    - /staff/admin/refresh/**
    - /manager/other/elasticsearch
    - /manager/other/customWords
    - /druid/**
    - /swagger-ui.html
    - /swagger-ui/index.html
    - /swagger-ui.html
    - /v3/api-docs
    - /v3/api-docs.yaml
    - /swagger-resources/**
    - /swagger/**
    - /webjars/**
    - /v2/api-docs
    - /configuration/ui
    - /boot-admin
    - /**/*.js
    - /**/*.css
    - /**/*.png
    - /**/*.ico
    - /staff/admin/login
    - /manager/setting/get/**
    - /product/**
    - /test/**
    - /manager/setting/**
    - /pc/file/super/**
    - /entdev/super/**
    - /staff/member/super/**
    - /staff/admin
springdoc:
  api-docs:
    # 是否敞开接口文档
    enabled: true
  swagger-ui:
    # 耐久化认证数据，假如设置为 true，它会保存授权数据而且不会在浏览器关闭/刷新时丢掉
    persistAuthorization: true
swagger:
  title: wteam API接口文档
  description: wteam Api Documentation
  version: 1.0.0
  termsOfServiceUrl: https://pickmall.cn
  contact:
    name: wteam
    url: https://pickmall.cn
    email: <EMAIL>

# Mybatis-plus
mybatis-plus:
  mapper-locations: classpath*:mapper/*.xml
  configuration:
    #缓存开启
    cache-enabled: true
    #日志
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# 日志
#logging:
#  config: classpath:logback-spring.xml
#  # 输出级别
#  level:
#    cn.lili: info
#  #    org.hibernate: debug
#  #    org.springframework: debug
#  file:
#    # 指定路径
#    path: lili-logs
#  logback:
#    rollingpolicy:
#      # 最大保存天数
#      max-history: 7
#      # 每个文件最大大小
#      max-file-size: 5MB

logging:
  level:
    com.ejlchina.searcher: DEBUG
lili:
  #验证码设置
  verification-code:
    #图形验证码有效时间 秒 包含滑块验证码有效时间， 以及验证通过之后，缓存中存储的验证结果有效时间
    effectiveTime: 300
    #水印
    watermark: LILI-SHOP
    #干扰项数量 最大2 默认0
    interfereNum: 0
    #允许误差像素
    faultTolerant: 3
  #短信模版配置
  sms:
    #登录
    LOGIN: SMS_205755300
    #注册
    REGISTER: SMS_205755298
    #找回密码
    FIND_USER: SMS_205755301
    #设置密码
    UPDATE_PASSWORD: SMS_205755297
    #支付密码
    WALLET_PASSWORD: SMS_205755301
  system:
    isTestModel: true
  statistics:
    # 在线人数统计 X 小时。这里设置48，即统计过去48小时每小时在线人数
    onlineMember: 48
    # 当前在线人数刷新时间间隔，单位秒，设置为600，则每10分钟刷新一次
    currentOnlineUpdate: 600
  #qq lbs 申请
  lbs:
    key: 4BYBZ-7MT6S-PUAOA-6BNWL-FJUD7-UUFXT
    sk: zhNKVrJK6UPOhqIjn8AQvG37b9sz6
  #域名
  domain:
    pc: https://pc.b2b2c.pickmall.cn
    wap: https://m.b2b2c.pickmall.cn
    store: https://store.b2b2c.pickmall.cn
    admin: https://admin.b2b2c.pickmall.cn
  #api地址
  api:
    buyer: https://buyer-api.pickmall.cn
    common: https://common-api.pickmall.cn
    manager: https://admin-api.pickmall.cn
    store: https://store-api.pickmall.cn

  # jwt 细节设定
  jwt-setting:
    # token过期时间（分钟）
    tokenExpireTime: 60

  # 使用Spring @Cacheable注解失效时间
  cache:
    # 过期时间 单位秒 永久不过期设为-1
    timeout: 1500
  #多线程配置
  thread:
    corePoolSize: 5
    maxPoolSize: 50
    queueCapacity: 50
  data:
    elasticsearch:
      cluster-name: elasticsearch
      cluster-nodes: 127.0.0.1:9200
      index:
        number-of-replicas: 0
        number-of-shards: 3
      index-prefix: lili
      schema: http
    #      account:
    #        username: elastic
    #        password: LiLiShopES
    logstash:
      server: 127.0.0.1:4560
    rocketmq:
      promotion-topic: lili_promotion_topic
      promotion-group: lili_promotion_group
      msg-ext-topic: lili_msg_topic
      msg-ext-group: lili_msg_group
      goods-topic: lili_goods_topic
      goods-group: lili_goods_group
      order-topic: lili_order_topic
      order-group: lili_order_group
      member-topic: lili_member_topic
      member-group: lili_member_group
      other-topic: lili_other_topic
      other-group: lili_other_group
      notice-topic: lili_notice_topic
      notice-group: lili_notice_group
      notice-send-topic: lili_send_notice_topic
      notice-send-group: lili_send_notice_group
khai:
  #域名
  #  domain:
  #    pc: https://pc.b2b2c.pickmall.cn
  #    wap: https://m.b2b2c.pickmall.cn
  #    store: https://store.b2b2c.pickmall.cn
  #    admin: https://admin.b2b2c.pickmall.cn
  #api地址
  #  api:
  #    buyer: https://buyer-api.pickmall.cn
  #    common: https://common-api.pickmall.cn
  #    manager: https://admin-api.pickmall.cn
  #    store: https://store-api.pickmall.cn

  # jwt 细节设定
  jwt-setting:
    # token过期时间（分钟）
    tokenExpireTime: 3600

  # 使用Spring @Cacheable注解失效时间
  cache:
    # 过期时间 单位秒 永久不过期设为-1
    timeout: 1500
  #多线程配置
#  thread:
#    corePoolSize: 5
#    maxPoolSize: 50
#    queueCapacity: 50
#  data:
#    elasticsearch:
#      cluster-name: elasticsearch
#      cluster-nodes: 127.0.0.1:9200
#      index:
#        number-of-replicas: 0
#        number-of-shards: 3
#      index-prefix: lili
#      schema: http
#    #      account:
#    #        username: elastic
#    #        password: LiLiShopES
#    logstash:
#      server: 127.0.0.1:4560
#    rocketmq:
#      promotion-topic: lili_promotion_topic
#      promotion-group: lili_promotion_group
#      msg-ext-topic: lili_msg_topic
#      msg-ext-group: lili_msg_group
#      goods-topic: lili_goods_topic
#      goods-group: lili_goods_group
#      order-topic: lili_order_topic
#      order-group: lili_order_group
#      member-topic: lili_member_topic
#      member-group: lili_member_group
#      other-topic: lili_other_topic
#      other-group: lili_other_group
#      notice-topic: lili_notice_topic
#      notice-group: lili_notice_group
#      notice-send-topic: lili_send_notice_topic
#      notice-send-group: lili_send_notice_group
#      after-sale-topic: lili_after_sale_topic
#      after-sale-group: lili_after_sale_group
#rocketmq:
#  name-server: 127.0.0.1:9876
#  producer:
#    group: lili_group
#    send-message-timeout: 30000
#
#xxl:
#  job:
#    admin:
#      addresses: http://127.0.0.1:9001/xxl-job-admin
#    executor:
#      appname: xxl-job-executor-lilishop
#      address:
#      ip:
#      port: 8891
#      logpath: ./xxl-job/executor
#      logretentiondays: 7
